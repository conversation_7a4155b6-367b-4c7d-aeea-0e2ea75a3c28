import React from "react";
import { CheckboxGroup } from "@heroui/checkbox";
import { tv } from "@heroui/theme";
import { Chip } from "@heroui/chip";
import { VisuallyHidden } from "@react-aria/visually-hidden";
import { siteConfig } from "@/config/site";
import { CheckIcon } from "@/components/icons";



interface CustomCheckboxProps {
  value: string;
  children?: React.ReactNode;
  isSelected?: boolean;
  isFocusVisible?: boolean;
  getBaseProps?: () => any;
  getLabelProps?: () => any;
  getInputProps?: () => any;
}

export const CustomCheckbox: React.FC<CustomCheckboxProps> = (props) => {
  const checkbox = tv({
    slots: {
      base: "border-default hover:bg-default-200",
      content: "text-default-500",
    },
    variants: {
      isSelected: {
        true: {
          base: "border-primary bg-primary hover:bg-primary-500 hover:border-primary-500",
          content: "text-primary-foreground pl-1",
        },
      },
      isFocusVisible: {
        true: {
          base: "outline-solid outline-transparent ring-2 ring-focus ring-offset-2 ring-offset-background",
        },
      },
    },
  });

  const { children, isSelected, isFocusVisible, getBaseProps, getLabelProps, getInputProps } = props;

  const styles = checkbox({ isSelected, isFocusVisible });

  return (
    <label {...(getBaseProps ? getBaseProps() : {})}>
      <VisuallyHidden>
        <input {...(getInputProps ? getInputProps() : {})} />
      </VisuallyHidden>
      <Chip
        classNames={{
          base: styles.base(),
          content: styles.content(),
        }}
        color="primary"
        startContent={isSelected ? <CheckIcon className="ml-1" /> : null}
        variant="faded"
        {...(getLabelProps ? getLabelProps() : {})}
      >
        {children ? children : isSelected ? "已启用" : "已禁用"}
      </Chip>
    </label>
  );
};

interface ControlCheckboxGroupProps {
  label?: string;
  orientation?: "horizontal" | "vertical";
  value?: string[];
  onChange?: (value: string[]) => void;
  options?: Array<{
    value: string;
    label: string;
  }>;
  className?: string;
}

export const ControlCheckboxGroup: React.FC<ControlCheckboxGroupProps> = ({
  label = "选择选项",
  orientation = "horizontal",
  value = [],
  onChange,
  options = siteConfig.checkboxOptions.amenities,
  className,
}) => {
  const [groupSelected, setGroupSelected] = React.useState<string[]>(value);

  React.useEffect(() => {
    setGroupSelected(value);
  }, [value]);

  const handleChange = (newValue: string[]) => {
    setGroupSelected(newValue);
    onChange?.(newValue);
  };

  return (
    <div className={`flex flex-col gap-1 w-full ${className || ""}`}>
      <CheckboxGroup
        className="gap-1"
        label={label}
        orientation={orientation}
        value={groupSelected}
        onChange={handleChange}
      >
        {options.map((option) => (
          <CustomCheckbox key={option.value} value={option.value}>
            {option.label}
          </CustomCheckbox>
        ))}
      </CheckboxGroup>
      <p className="mt-4 ml-1 text-default-500">
        已选择: {groupSelected.join(", ")}
      </p>
    </div>
  );
};

export default ControlCheckboxGroup;
